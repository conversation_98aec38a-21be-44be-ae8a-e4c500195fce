# استعادة شرط الموافقة على الشروط والأحكام - Email Agreement Validation Restored

## 🔍 المشكلة المكتشفة

عند البحث عن شرط الموافقة على الشروط والأحكام لطلب New Email، وُجد أن **الشروط كانت مفقودة**!

### ما كان مفقود:
1. **constraint في النموذج** للتحقق من الموافقة عند الحفظ
2. **التحقق في دالة action_submit** لمنع الإرسال بدون موافقة
3. **إخفاء زر Submit** في العرض عند عدم الموافقة

## ✅ الشروط المستعادة

### 1. Constraint في النموذج (`models/request/base.py`):
```python
@api.constrains('email_type_new', 'email_agreement_accepted')
def _check_email_agreement_accepted(self):
    """Ensure email agreement is accepted when New Email is selected"""
    for record in self:
        if record.email_type_new and not record.email_agreement_accepted:
            raise ValidationError(_('You must agree to the terms and conditions to request a new email.'))
```

### 2. التحقق في دالة create:
```python
@api.model
def create(self, vals):
    # Check email agreement before creating
    if vals.get('email_type_new') and not vals.get('email_agreement_accepted'):
        raise ValidationError(_('You must agree to the terms and conditions to request a new email.'))
    # ... rest of create method
```

### 3. التحقق في دالة write:
```python
def write(self, vals):
    # Check email agreement before updating
    for record in self:
        # Check if we're setting email_type_new to True without agreement
        if vals.get('email_type_new') and not vals.get('email_agreement_accepted', record.email_agreement_accepted):
            raise ValidationError(_('You must agree to the terms and conditions to request a new email.'))
        # Check if we're unsetting agreement while email_type_new is True
        if 'email_agreement_accepted' in vals and not vals['email_agreement_accepted'] and record.email_type_new:
            raise ValidationError(_('You must agree to the terms and conditions to request a new email.'))
    return super(BSSICRequest, self).write(vals)
```

### 4. التحقق في دالة action_submit (`models/request/workflow.py`):
```python
def action_submit(self):
    """Submit request for approval"""
    # Check email agreement before submitting
    if self.email_type_new and not self.email_agreement_accepted:
        raise ValidationError(_('You must agree to the terms and conditions to submit a new email request.'))
    # ... rest of submit method
```

### 5. إخفاء زر Submit في العرض (`views/email_request_views.xml`):
```xml
<button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"
        attrs="{'invisible': ['&amp;', ('email_type_new', '=', True), ('email_agreement_accepted', '=', False)]}"/>
```

## 🔒 طبقات الحماية المستعادة

### الطبقة الأولى - العرض:
- **زر Submit مخفي** عند اختيار "New Email" بدون موافقة
- **منع المستخدم من المحاولة** من الأساس

### الطبقة الثانية - النموذج:
- **constraint عند الحفظ** يمنع حفظ الطلب بدون موافقة
- **التحقق في create/write** لضمان عدم تجاوز القيود

### الطبقة الثالثة - سير العمل:
- **التحقق في action_submit** كطبقة حماية إضافية
- **رسالة خطأ واضحة** عند المحاولة

## 🎯 النتيجة النهائية

الآن عند اختيار "New Email":

### ✅ **مع الموافقة على الشروط:**
- زر Submit يظهر ويعمل بشكل طبيعي
- يمكن حفظ وإرسال الطلب
- سير العمل يتقدم كما هو متوقع

### ❌ **بدون الموافقة على الشروط:**
- زر Submit مخفي تماماً
- لا يمكن حفظ الطلب (رسالة خطأ)
- لا يمكن إرسال الطلب (رسالة خطأ)
- حماية شاملة في جميع المستويات

## 📝 رسائل الخطأ

### عند الحفظ/التعديل:
```
"You must agree to the terms and conditions to request a new email."
```

### عند الإرسال:
```
"You must agree to the terms and conditions to submit a new email request."
```

**الآن النظام محمي بالكامل ويضمن الموافقة على الشروط قبل المتابعة! 🔒**
