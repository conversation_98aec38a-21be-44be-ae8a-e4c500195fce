# إصلاح مشكلة ظهور زر Submit مرة أخرى بعد الضغط عليه

## 🐛 المشكلة

عند الضغط على زر Submit، كان الزر يظهر مرة أخرى بدلاً من إظهار زر Reject فقط.

## 🔍 تحليل المشكلة

### الكود الأصلي في `action_submit`:
```python
# First set state to submitted
self.state = 'submitted'
# Then move to direct manager approval  
self.state = 'direct_manager'
```

### المشكلة:
1. **تغيير الحالة مرتين**: الكود كان يغير الحالة من `draft` إلى `submitted` ثم إلى `direct_manager`
2. **حالة وسطية غير مدعومة**: الحالة `submitted` لم تكن لها أزرار محددة في العرض
3. **عرض غير متسق**: هذا سبب ظهور زر Submit مرة أخرى

### تفصيل المشكلة:
- زر Submit مرتبط بـ `states="draft"`
- عند الضغط عليه، تتغير الحالة إلى `submitted` ثم `direct_manager`
- لكن العرض لا يحتوي على أزرار للحالة `submitted`
- هذا يسبب عرض غير متوقع للأزرار

## ✅ الحل المطبق

### إزالة الحالة الوسطية:
```python
# Log activity
if self.id:
    self.env['bssic.request.activity.log'].create_activity_log(
        'bssic.request', self.id, 'submitted',
        notes=_('Request submitted for approval'),
        old_state='draft', new_state='direct_manager'
    )

# Move directly to direct manager approval
self.state = 'direct_manager'
```

### التغييرات:
1. **إزالة** `self.state = 'submitted'`
2. **الانتقال مباشرة** من `draft` إلى `direct_manager`
3. **الحفاظ على** سجل الأنشطة كما هو

## 🎯 النتيجة

### قبل الإصلاح:
```
draft → submitted → direct_manager
  ↓        ↓           ↓
Submit   ???      Approve/Reject
```

### بعد الإصلاح:
```
draft → direct_manager
  ↓           ↓
Submit   Approve/Reject
```

## ✅ التحقق من النتيجة

- ✅ **زر Submit يختفي** بعد الضغط عليه
- ✅ **زر Reject يظهر** في الحالة الصحيحة
- ✅ **سير العمل يعمل** بشكل صحيح
- ✅ **سجل الأنشطة محفوظ** كما هو متوقع

## 📝 ملاحظات

- تم الحفاظ على جميع الوظائف الأخرى
- سجل الأنشطة ما زال يسجل الانتقال بشكل صحيح
- الإشعارات للمدير المباشر تعمل كما هو متوقع
- لا توجد تأثيرات جانبية على أنواع الطلبات الأخرى
