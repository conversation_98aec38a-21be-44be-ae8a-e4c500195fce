# تغييرات نوع طلب البريد الإلكتروني - Email Type Changes

## 📋 ملخص التغييرات

تم تغيير شكل عرض أنواع طلبات البريد الإلكتروني من **radio buttons** إلى **checkboxes** كما طُلب.

## 🔧 التعديلات المطبقة

### 1. تعديل النموذج (`models/request/base.py`)
- إضافة حقول Boolean جديدة:
  - `email_type_new` - بريد إلكتروني جديد
  - `email_type_password_reset` - إعادة تعيين كلمة المرور
  - `email_type_2fa_reset` - إعادة تعيين المصادقة الثنائية

- الحفاظ على الحقل الأصلي `email_type` للتوافق مع الإصدارات السابقة
- إضافة دالة `_compute_email_type` لحساب القيمة بناءً على الـ checkboxes
- إضافة دالة `_onchange_email_type_checkboxes` للتعامل مع تغييرات الـ checkboxes

### 2. تعديل العرض (`views/request_views.xml`)
- تغيير عرض الحقول من `widget="radio"` إلى checkboxes عادية
- تحديث شروط إظهار اتفاقية البريد الإلكتروني لتعتمد على `email_type_new`

### 3. إضافة ملف التحويل (`data/email_type_migration.xml`)
- دالة تحويل البيانات الموجودة من النظام القديم إلى الجديد
- تحديث `__manifest__.py` لتضمين ملف التحويل

## 🎯 النتيجة

الآن عند إنشاء طلب Email Request، سيظهر للمستخدم:

✅ **قبل التعديل:**
```
○ New Email
○ Password Reset  
○ Two-Factor Authentication Reset
```

✅ **بعد التعديل:**
```
☐ New Email
☐ Password Reset
☐ Two-Factor Authentication Reset
```

## 🔄 التوافق مع الإصدارات السابقة

- تم الحفاظ على الحقل الأصلي `email_type` 
- البيانات الموجودة ستُحول تلقائياً عند التحديث
- جميع الوظائف الأخرى تعمل بنفس الطريقة

## 📝 ملاحظات

- التغيير يؤثر فقط على **الشكل** وليس على طريقة العمل
- اتفاقية البريد الإلكتروني تظهر فقط عند اختيار "New Email"
- جميع عمليات التحقق والموافقات تعمل كما هو متوقع
